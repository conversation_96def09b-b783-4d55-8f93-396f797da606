<script lang="ts">
  import { cn } from "$lib/utils";
  import { X, Edit3 } from 'lucide-svelte';
  import type { QuickFilterTag } from '$lib/types/filterConfig';
  import { createEventDispatcher } from 'svelte';

  // Props
  export let tag: QuickFilterTag;
  export let showActions = false;
  export let onEdit: ((tag: QuickFilterTag) => void) | null = null;
  export let onDelete: ((tag: QuickFilterTag) => void) | null = null;

  // 拖拽相关
  export let draggableEnabled: boolean = true;
  export let isDropTarget: boolean = false;
  export let dropPosition: 'before' | 'after' = 'before';

  const dispatch = createEventDispatcher<{
    tagdragstart: { id: string };
    tagdragover: { id: string; clientX: number; bounding: DOMRect };
    tagdrop: { id: string; clientX: number; bounding: DOMRect };
    tagdragend: { id: string };
  }>();

  let isDragging = false;
  let suppressClick = false;

  // 获取颜色样式
  function getColorClasses(color: string = 'blue') {
    const colorMap: Record<string, {
      bg: string,
      activeBg: string,
      text: string,
      activeText: string,
      border: string,
      activeBorder: string
    }> = {
      blue: {
        bg: "bg-blue-50",
        activeBg: "bg-blue-500",
        text: "text-blue-700",
        activeText: "text-white",
        border: "border-blue-200",
        activeBorder: "border-blue-500"
      },
      green: {
        bg: "bg-green-50",
        activeBg: "bg-green-500",
        text: "text-green-700",
        activeText: "text-white",
        border: "border-green-200",
        activeBorder: "border-green-500"
      },
      purple: {
        bg: "bg-purple-50",
        activeBg: "bg-purple-500",
        text: "text-purple-700",
        activeText: "text-white",
        border: "border-purple-200",
        activeBorder: "border-purple-500"
      },
      orange: {
        bg: "bg-orange-50",
        activeBg: "bg-orange-500",
        text: "text-orange-700",
        activeText: "text-white",
        border: "border-orange-200",
        activeBorder: "border-orange-500"
      },
      gray: {
        bg: "bg-gray-50",
        activeBg: "bg-gray-500",
        text: "text-gray-700",
        activeText: "text-white",
        border: "border-gray-200",
        activeBorder: "border-gray-500"
      }
    };

    return colorMap[color] || colorMap.blue;
  }

  const colorClasses = getColorClasses(tag.color);

  // 处理点击
  function handleClick() {
    if (suppressClick) {
      suppressClick = false;
      return;
    }
    if (tag.onClick) {
      tag.onClick();
    }
  }

  // 处理编辑
  function handleEdit(event: MouseEvent) {
    event.stopPropagation();
    if (onEdit) {
      onEdit(tag);
    }
  }

  // 处理删除
  function handleDelete(event: MouseEvent) {
    event.stopPropagation();
    if (onDelete) {
      onDelete(tag);
    }
  }

  // 拖拽事件处理
  function onDragStart(e: DragEvent) {
    if (!draggableEnabled) return;
    isDragging = true;
    suppressClick = true;
    e.dataTransfer?.setData('text/plain', tag.id);
    e.dataTransfer?.setDragImage((e.target as HTMLElement), 10, 10);
    dispatch('tagdragstart', { id: tag.id });
  }
  function onDragOver(e: DragEvent) {
    if (!draggableEnabled) return;
    e.preventDefault();
    const target = e.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    dispatch('tagdragover', { id: tag.id, clientX: e.clientX, bounding: rect });
  }
  function onDrop(e: DragEvent) {
    if (!draggableEnabled) return;
    e.preventDefault();
    const target = e.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    dispatch('tagdrop', { id: tag.id, clientX: e.clientX, bounding: rect });
  }
  function onDragEnd() {
    if (!draggableEnabled) return;
    isDragging = false;
    dispatch('tagdragend', { id: tag.id });
  }
</script>

<div
  class="relative"
  role="listitem"
  aria-grabbed={isDragging}
  ondragover={onDragOver}
  ondrop={onDrop}
>
  <button
    class={cn(
      "inline-flex items-center justify-center px-2 py-1 rounded-md border transition-all duration-200 cursor-move select-none",
      "hover:shadow focus:outline-none focus:ring-1 focus:ring-offset-1",
      tag.isActive
        ? `${colorClasses.activeBg} ${colorClasses.activeText} ${colorClasses.activeBorder} shadow`
        : `${colorClasses.bg} ${colorClasses.text} ${colorClasses.border} hover:${colorClasses.activeBg} hover:${colorClasses.activeText}`,
      "min-h-[24px] text-xs font-medium",
      isDragging ? "opacity-70" : ""
    )}
    onclick={handleClick}
    draggable={draggableEnabled}
    data-id={tag.id}
    ondragstart={onDragStart}
    ondragend={onDragEnd}
    aria-label={`应用筛选: ${tag.label}`}
  >
    <div class="flex items-center gap-1">
      <span>{tag.label}</span>
      {#if tag.count !== undefined}
        <span class={cn(
          "text-[10px] px-1.5 py-0.5 rounded",
          tag.isActive
            ? "bg-white bg-opacity-20"
            : "bg-gray-100"
        )}>
          {tag.count}
        </span>
      {/if}
    </div>
  </button>

  <!-- 拖拽插入位置指示 -->
  {#if isDropTarget && dropPosition === 'before'}
    <div class="pointer-events-none absolute inset-y-0 -left-1 border-l-2 border-blue-400"></div>
  {/if}
  {#if isDropTarget && dropPosition === 'after'}
    <div class="pointer-events-none absolute inset-y-0 -right-1 border-r-2 border-blue-400"></div>
  {/if}

  <!-- 操作按钮（由父组件控制是否显示） -->
  {#if showActions && (onEdit || onDelete)}
    <div class="absolute -top-1 -right-1 flex gap-0.5">
      {#if onEdit}
        <button
          onclick={handleEdit}
          class="w-4 h-4 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-sm"
          aria-label="编辑筛选配置"
          title="编辑"
        >
          <Edit3 class="h-2 w-2" />
        </button>
      {/if}

      {#if onDelete}
        <button
          onclick={handleDelete}
          class="w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors shadow-sm"
          aria-label="删除筛选配置"
          title="删除"
        >
          <X class="h-2 w-2" />
        </button>
      {/if}
    </div>
  {/if}
</div>

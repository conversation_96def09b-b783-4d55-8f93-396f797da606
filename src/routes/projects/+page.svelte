<script lang="ts">
  import { onMount } from 'svelte';
  import {
    projectManagementService,
    type ProjectQuery,
    type ProjectWithDetails,
    type ProjectPersonnelWithDetails
  } from '$lib/services/projectManagementService';
  import { Button } from '$lib/components/ui/button';
  import Input from '$lib/components/ui/input.svelte';
  import Table from '$lib/components/ui/table.svelte';
  import StatsBadge from "$lib/components/project/StatsBadge.svelte";
  import ExportProjectsDialog from "$lib/components/project/ExportProjectsDialog.svelte";
  import FilterConfigDialog from "$lib/components/filter/FilterConfigDialog.svelte";
  import QuickFilterTag from "$lib/components/filter/QuickFilterTag.svelte";

  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { staffService } from '$lib/services/staffService';
  import { fileSystemService } from '$lib/services/fileSystemService';
  import { goto } from '$app/navigation';
  import { PlusCircle, Search, Pencil, Trash, RefreshCw, FileDown, Folder, ClipboardList, Upload, AlertTriangle, CheckCircle } from 'lucide-svelte';
  import { projectFilterStore, saveFilterState } from '$lib/stores/filterStore';
  import CsvImportDialog from '$lib/components/csv-import/CsvImportDialog.svelte';
  import { filterConfigService } from '$lib/services/filterConfigService';
  import type { FilterConfig, QuickFilterTag as QuickFilterTagType } from '$lib/types/filterConfig';

  // 状态管理
  let projects = $state<ProjectWithDetails[]>([]);
  let allProjects = $state<ProjectWithDetails[]>([]); // 存储所有项目，用于统计
  let isLoading = $state(false);
  let isDeleting = $state(false);
  let error = $state<string | null>(null);
  let total = $state(0);
  let totalAllProjects = $state(0); // 所有项目的总数
  let currentPage = $state(1);
  let pageSize = $state(50);
  let showDeleteConfirm = $state(false);
  let projectToDelete = $state<{id: string, name: string} | null>(null);
  let confirmProjectName = $state('');
  let deleteConfirmError = $state<string | null>(null);
  let showExportDialog = $state(false); // 控制导出对话框显示
  let showCsvImportDialog = $state(false); // 控制CSV导入对话框显示
  let showFilterConfigDialog = $state(false); // 控制筛选配置对话框显示

  // 筛选配置相关状态
  let filterConfigs = $state<FilterConfig[]>([]);
  let currentFilterConfig = $state<FilterConfig | null>(null);
  let customFilterTags = $state<QuickFilterTagType[]>([]);

  // 当前活动的过滤器
  let activeFilter = $state<'all' | 'ongoing' | 'finished' | 'recruiting'>('ongoing');

  // 快捷筛选编辑模式
  let editMode = $state(false);

  // 项目统计
  // 拖拽排序状态
  let draggingId = $state<string | null>(null);
  let dropTargetId = $state<string | null>(null);
  let dropPositionForTarget = $state<'before' | 'after' | null>(null);

  let projectStats = $state({
    totalProjects: 0,
    ongoingProjects: 0,
    finishedProjects: 0,
    recruitingProjects: 0,
    diseaseDistribution: [] as {disease: string, count: number}[],
    stageDistribution: [] as {stage: string, count: number}[],
    // 各状态下的疾病分布
    allDiseaseDistribution: [] as {disease: string, count: number}[],
    ongoingDiseaseDistribution: [] as {disease: string, count: number}[],
    finishedDiseaseDistribution: [] as {disease: string, count: number}[],
    recruitingDiseaseDistribution: [] as {disease: string, count: number}[]
  });

  // 筛选条件
  let searchName = $state('');
  let selectedDiseaseId = $state<number | null>(null);
  let selectedStageId = $state<number | null>(null);
  let selectedStatusId = $state<number | null>(null);
  let selectedRecruitmentStatusId = $state<number | null>(null);
  let sortBy = $state('project_name');
  let sortOrder = $state<'asc' | 'desc'>('asc');

  // 字典项
  let diseases = $state<{item_id: number, item_value: string}[]>([]);
  let stages = $state<{item_id: number, item_value: string}[]>([]);
  let statuses = $state<{item_id: number, item_value: string}[]>([]);
  let recruitmentStatuses = $state<{item_id: number, item_value: string}[]>([]);
  let sponsors = $state<{item_id: number, item_value: string}[]>([]);
  let piStaff = $state<{id: number, name: string}[]>([]);

  // 额外筛选（申办方、PI）
  let selectedSponsorIds = $state<number[] | null>(null);
  let selectedPiIds = $state<number[] | null>(null);

  // 加载所有项目（用于统计）
  async function loadAllProjects() {
    try {
      // 构建查询参数，不包含任何筛选条件
      const query: ProjectQuery = {
        page: 1,
        page_size: 1000, // 设置较大的页面大小以获取所有项目
        sort_by: 'project_name',
        sort_order: 'asc'
      };

      // 调用服务获取所有项目列表
      const result = await projectManagementService.getProjects(query);

      // 获取每个项目的详细信息，包括研究人员和申办方
      const projectsWithDetails = await Promise.all(
        result.items.map(async (project) => {
          if (project.project.project_id) {
            try {
              // 获取项目详情
              const details = await projectManagementService.getProjectDetails(project.project.project_id);
              if (details) {
                // 如果成功获取详情，更新人员信息和申办方信息
                return {
                  ...project,
                  personnel: details.personnel || [],
                  sponsors: details.sponsors || []
                };
              }
            } catch (error) {
              console.error(`获取项目 ${project.project.project_id} 详情失败:`, error);
            }
          }
          return project;
        })
      );

      allProjects = projectsWithDetails;
      totalAllProjects = result.total;

      // 更新项目统计（基于所有项目）
      updateAllProjectStats();
    } catch (err) {
      console.error('加载所有项目失败:', err);
    }
  }

  // 加载筛选后的项目列表
  async function loadProjects() {
    isLoading = true;
    error = null;

    try {
      // 构建查询参数
      const query: ProjectQuery = {
        name: searchName || undefined,
        disease_item_id: selectedDiseaseId || undefined,
        project_stage_item_id: selectedStageId || undefined,
        project_status_item_id: selectedStatusId || undefined,
        recruitment_status_item_id: selectedRecruitmentStatusId || undefined,
        sponsor_item_ids: selectedSponsorIds || undefined,
        pi_personnel_ids: selectedPiIds || undefined,
        page: currentPage,
        page_size: pageSize,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      console.log('[快捷筛选] loadProjects 使用查询参数:', JSON.stringify(query));

      // 调用服务获取项目列表
      const result = await projectManagementService.getProjects(query);

      // 获取每个项目的详细信息，包括研究人员和申办方
      const projectsWithDetails = await Promise.all(
        result.items.map(async (project) => {
          if (project.project.project_id) {
            try {
              // 获取项目详情
              const details = await projectManagementService.getProjectDetails(project.project.project_id);
              if (details) {
                // 如果成功获取详情，更新人员信息和申办方信息
                return {
                  ...project,
                  personnel: details.personnel || [],
                  sponsors: details.sponsors || []
                };
              }
            } catch (error) {
              console.error(`获取项目 ${project.project.project_id} 详情失败:`, error);
            }
          }
          return project;
        })
      );

      projects = projectsWithDetails;
      total = result.total;
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isLoading = false;
    }
  }

  // 更新所有项目的统计信息（基于allProjects）
  function updateAllProjectStats() {
    if (!allProjects || allProjects.length === 0) return;

    // 计算基本统计
    projectStats.totalProjects = totalAllProjects;

    // 计算在研项目数量（项目状态为"在研"的项目）
    const ongoingProjects = allProjects.filter(p =>
      p.project_status?.item_value === '在研'
    );
    projectStats.ongoingProjects = ongoingProjects.length;

    // 计算已结束项目数量（项目状态为"已结束"的项目）
    const finishedProjects = allProjects.filter(p =>
      p.project_status?.item_value === '已结束'
    );
    projectStats.finishedProjects = finishedProjects.length;

    // 计算招募中项目数量
    const recruitingProjects = allProjects.filter(p =>
      p.recruitment_status?.item_value === '招募中'
    );
    projectStats.recruitingProjects = recruitingProjects.length;

    // 计算所有项目的疾病分布
    const diseaseMap = new Map<string, number>();
    allProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = diseaseMap.get(p.disease.item_value) || 0;
        diseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.diseaseDistribution = Array.from(diseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算所有项目的疾病分布（用于"总项目数"过滤器）
    projectStats.allDiseaseDistribution = [...projectStats.diseaseDistribution];

    // 计算在研项目的疾病分布
    const ongoingDiseaseMap = new Map<string, number>();
    ongoingProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = ongoingDiseaseMap.get(p.disease.item_value) || 0;
        ongoingDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.ongoingDiseaseDistribution = Array.from(ongoingDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算已结束项目的疾病分布
    const finishedDiseaseMap = new Map<string, number>();
    finishedProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = finishedDiseaseMap.get(p.disease.item_value) || 0;
        finishedDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.finishedDiseaseDistribution = Array.from(finishedDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算招募中项目的疾病分布
    const recruitingDiseaseMap = new Map<string, number>();
    recruitingProjects.forEach(p => {
      if (p.disease?.item_value) {
        const count = recruitingDiseaseMap.get(p.disease.item_value) || 0;
        recruitingDiseaseMap.set(p.disease.item_value, count + 1);
      }
    });
    projectStats.recruitingDiseaseDistribution = Array.from(recruitingDiseaseMap.entries())
      .map(([disease, count]) => ({ disease, count }))
      .sort((a, b) => b.count - a.count);

    // 计算阶段分布
    const stageMap = new Map<string, number>();
    allProjects.forEach(p => {
      if (p.project_stage?.item_value) {
        const count = stageMap.get(p.project_stage.item_value) || 0;
        stageMap.set(p.project_stage.item_value, count + 1);
      }
    });
    projectStats.stageDistribution = Array.from(stageMap.entries())
      .map(([stage, count]) => ({ stage, count }))
      .sort((a, b) => b.count - a.count);
  }

  // 加载字典项
  async function loadDictionaryItems() {
    try {
      // 加载疾病字典
      const diseasesDict = await sqliteDictionaryService.getDictByName('疾病');
      console.log('疾病字典:', diseasesDict);
      if (diseasesDict && diseasesDict.items) {
        diseases = diseasesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('加载的疾病列表:', diseases);
      }

      // 加载项目阶段字典
      console.log('开始加载研究分期字典...');
      try {
        const stagesDict = await sqliteDictionaryService.getDictByName('研究分期');
        console.log('研究分期字典详情:', JSON.stringify(stagesDict, null, 2));

        if (stagesDict && stagesDict.items) {
          console.log('研究分期字典项数量:', stagesDict.items.length);
          console.log('研究分期字典项示例:', stagesDict.items.slice(0, 3));

          stages = stagesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的研究分期列表:', stages);
        } else {
          console.error('研究分期字典为空或没有items属性');
        }
      } catch (err) {
        console.error('加载研究分期字典时出错:', err);
      }

      // 加载项目状态字典（使用研究阶段字典）
      console.log('开始加载项目状态字典...');
      try {
        // 直接加载"研究阶段"字典
        const statusesDict = await sqliteDictionaryService.getDictByName('研究阶段');
        console.log('研究阶段字典详情:', JSON.stringify(statusesDict, null, 2));

        if (statusesDict && statusesDict.items) {
          console.log('研究阶段字典项数量:', statusesDict.items.length);
          console.log('研究阶段字典项示例:', statusesDict.items.slice(0, 3));

          statuses = statusesDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
          console.log('加载的研究阶段列表:', statuses);
        } else {
          console.error('研究阶段字典为空或没有items属性');
          // 如果无法加载，则使用空数组
          statuses = [];
        }
      } catch (err) {
        console.error('加载研究阶段字典时出错:', err);
        // 出错时使用空数组
        statuses = [];
      }

      // 加载招募状态字典
      console.log('开始加载招募状态字典...');
      try {
        // 首先尝试加载"招募状态"字典
        let recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募状态');
        console.log('招募状态字典详情:', JSON.stringify(recruitmentStatusesDict, null, 2));

        // 如果"招募状态"字典不存在或为空，则尝试加载"招募公司"字典作为备用
        if (!recruitmentStatusesDict || !recruitmentStatusesDict.items || recruitmentStatusesDict.items.length === 0) {
          console.log('招募状态字典为空，尝试加载招募公司字典作为备用...');
          recruitmentStatusesDict = await sqliteDictionaryService.getDictByName('招募公司');
          console.log('招募公司字典详情(作为招募状态备用):', JSON.stringify(recruitmentStatusesDict, null, 2));
        }

        if (recruitmentStatusesDict && recruitmentStatusesDict.items) {
          console.log('招募状态字典项数量:', recruitmentStatusesDict.items.length);
          console.log('招募状态字典项示例:', recruitmentStatusesDict.items.slice(0, 3));

        recruitmentStatuses = recruitmentStatusesDict.items.map(item => ({
          item_id: item.item_id || 0,
          item_value: item.value
        }));
        console.log('加载的招募状态列表:', recruitmentStatuses);
        } else {
          console.error('招募状态字典为空或没有items属性');
          // 如果仍然无法加载，则使用空数组
          recruitmentStatuses = [];
        }
      } catch (err) {
        console.error('加载招募状态字典时出错:', err);
        // 出错时使用空数组
        recruitmentStatuses = [];
      }

      // 加载申办方字典
      try {
        const sponsorsDict = await sqliteDictionaryService.getDictByName('申办方');
        if (sponsorsDict && sponsorsDict.items) {
          sponsors = sponsorsDict.items.map(item => ({
            item_id: item.item_id || 0,
            item_value: item.value
          }));
        }
      } catch (err) {
        console.error('加载申办方字典失败:', err);
        sponsors = [];
      }
    } catch (err) {
      console.error('加载字典项失败:', err);
    }
  }

  // 初始化项目管理表
  async function initProjectTables() {
    try {
      await projectManagementService.initTables();
      console.log('项目管理表初始化成功');
    } catch (err) {
      console.error('项目管理表初始化失败:', err);
    }
  }

  // 初始化筛选配置表
  async function initFilterConfigTables() {
    try {
      await filterConfigService.initTables();
      console.log('筛选配置表初始化成功');
    } catch (err) {
      console.error('筛选配置表初始化失败:', err);
    }
  }

  // 处理搜索
  function handleSearch() {
    currentPage = 1;
    loadProjects();
  }

  // 处理状态卡片点击
  function handleStatusCardClick(filter: 'all' | 'ongoing' | 'finished' | 'recruiting') {
    // 更新当前活动的过滤器
    activeFilter = filter;



    // 重置页码
    currentPage = 1;

    // 根据过滤器设置相应的状态ID
    switch (filter) {
      case 'all':
        // 显示所有项目，清除状态过滤
        selectedStatusId = null;
        selectedRecruitmentStatusId = null;
        break;
      case 'ongoing':
        // 查找"在研"状态的项目ID
        const ongoingStatus = statuses.find(s => s.item_value === '在研');
        selectedStatusId = ongoingStatus?.item_id || null;
        selectedRecruitmentStatusId = null;
        break;
      case 'finished':
        // 查找"已结束"状态的项目ID
        const finishedStatus = statuses.find(s => s.item_value === '已结束');
        selectedStatusId = finishedStatus?.item_id || null;
        selectedRecruitmentStatusId = null;
        break;
      case 'recruiting':
        // 查找"招募中"状态的项目ID
        const recruitingStatus = recruitmentStatuses.find(s => s.item_value === '招募中');
        selectedStatusId = null; // 清除研究阶段过滤
        selectedRecruitmentStatusId = recruitingStatus?.item_id || null;
        break;
    }

    // 加载过滤后的项目（不更新统计数据）
    loadProjects();
  }



  // 处理排序
  function handleSort(column: string) {
    if (sortBy === column) {
      // 如果已经按此列排序，则切换排序方向
      sortOrder = sortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      // 否则按此列升序排序
      sortBy = column;
      sortOrder = 'asc';
    }
    loadProjects();
  }

  // 筛选配置相关函数
  async function loadFilterConfigs() {
    try {
      filterConfigs = await filterConfigService.getFilterConfigs();
      updateCustomFilterTags();
    } catch (error) {
      console.error('加载筛选配置失败:', error);
    }
  }

  async function saveFilterConfig(config: FilterConfig) {
    try {
      if (currentFilterConfig?.id) {
        // 更新现有配置
        await filterConfigService.updateFilterConfig(currentFilterConfig.id, config);
      } else {
        // 创建新配置
        await filterConfigService.saveFilterConfig(config);
      }

      // 重新加载配置列表
      await loadFilterConfigs();

      // 关闭对话框
      showFilterConfigDialog = false;
      currentFilterConfig = null;
    } catch (error) {
      console.error('保存筛选配置失败:', error);
      alert('保存失败，请重试');
    }
  }

  async function deleteFilterConfig(id: string) {
    try {
      await filterConfigService.deleteFilterConfig(id);
      await loadFilterConfigs();
    } catch (error) {
      console.error('删除筛选配置失败:', error);
      alert('删除失败，请重试');
    }
  }

  function updateCustomFilterTags() {
    // 基础列表（按照 filterConfigs 原始顺序）
    const baseList = filterConfigs.map(config => ({
      id: config.id || '',
      label: config.name,
      color: 'gray',
      isActive: false,
      filterConfig: config,
      onClick: () => applyFilterConfig(config)
    }));

    // 获取当前内存顺序与本地持久化顺序
    let currentOrderIds: string[] = Array.isArray(customFilterTags) ? customFilterTags.map(t => t.id) : [];
    let savedOrderIds: string[] = [];
    try {
      const saved = localStorage.getItem('customFilterTagsOrder');
      if (saved) savedOrderIds = JSON.parse(saved);
    } catch (e) {
      console.warn('[快捷筛选] 读取本地排序失败:', e);
    }

    // 构建 id => tag 映射
    const map = new Map(baseList.map(t => [t.id, t] as const));

    // 优先使用当前顺序，其次使用本地存储顺序
    const preferredOrder = (currentOrderIds && currentOrderIds.length > 0)
      ? currentOrderIds
      : savedOrderIds;

    let ordered: typeof baseList = [];
    if (preferredOrder && preferredOrder.length > 0) {
      // 按照 preferredOrder 排序，忽略已不存在的ID
      for (const id of preferredOrder) {
        const item = map.get(id);
        if (item) ordered.push(item);
      }
      // 追加新出现的项
      for (const item of baseList) {
        if (!ordered.find(t => t.id === item.id)) ordered.push(item);
      }
    } else {
      ordered = baseList;
    }

    console.log('[快捷筛选] updateCustomFilterTags 应用排序：', ordered.map(t => t.id));
    customFilterTags = ordered;
  }

  function applyFilterConfig(config: FilterConfig) {
    // 应用筛选配置到当前查询
    const query = filterConfigService.applyFilterConfigToQuery(config);
    console.log('[快捷筛选] 点击标签，配置:', config);
    console.log('[快捷筛选] 解析得到查询参数:', query);

    // 判断是否包含后端支持的筛选字段
    const hasSupportedFilter = !!(
      query.name ||
      query.disease_item_id ||
      query.project_stage_item_id ||
      query.project_status_item_id ||
      query.recruitment_status_item_id ||
      (Array.isArray(query.sponsor_item_ids) && query.sponsor_item_ids.length) ||
      (Array.isArray(query.pi_personnel_ids) && query.pi_personnel_ids.length)
    );

    if (!hasSupportedFilter) {
      console.warn('[快捷筛选] 此筛选配置不包含当前列表支持的字段（项目名称/疾病/研究分期/项目状态/招募状态/申办方/PI），列表结果可能不变化');
    }

    // 更新状态
    selectedDiseaseId = query.disease_item_id || null;
    selectedStageId = query.project_stage_item_id || null;
    selectedStatusId = query.project_status_item_id || null;
    selectedRecruitmentStatusId = query.recruitment_status_item_id || null;
    selectedSponsorIds = query.sponsor_item_ids || null;
    selectedPiIds = query.pi_personnel_ids || null;
    searchName = query.name || '';

    // 重置页码并加载项目
    currentPage = 1;
    loadProjects();

    // 更新标签状态
    updateCustomFilterTags();
    customFilterTags = customFilterTags.map(tag => ({
      ...tag,
      isActive: tag.filterConfig?.id === config.id
    }));
  }

  // 获取排序图标
  function getSortIcon(column: string) {
    if (sortBy !== column) return '';
    return sortOrder === 'asc' ? '↑' : '↓';
  }

  // 按角色分组人员
  function groupPersonnelByRole(personnel: ProjectPersonnelWithDetails[]) {
    if (!personnel || personnel.length === 0) return [];

    const roleGroups: {
      roleId: number;
      roleName: string;
      personnel: Array<{
        index: number;
        person: ProjectPersonnelWithDetails;
      }>;
    }[] = [];

    // 遍历所有人员，按角色分组
    personnel.forEach((person, index) => {
      const roleId = person.role_item_id;
      const roleName = person.role?.item_value || '未知角色';

      // 查找该角色是否已存在于分组中
      let roleGroup = roleGroups.find(group => group.roleId === roleId);

      if (!roleGroup) {
        // 如果角色不存在，创建新的角色分组
        roleGroup = {
          roleId,
          roleName,
          personnel: []
        };
        roleGroups.push(roleGroup);
      }

      // 将人员添加到对应角色分组
      roleGroup.personnel.push({
        index,
        person
      });
    });

    // 按角色名称排序
    roleGroups.sort((a, b) => a.roleName.localeCompare(b.roleName));

    return roleGroups;
  }



  // 处理页码变化
  function handlePageChange(page: number) {
    currentPage = page;
    loadProjects();
  }

  // 处理查看项目
  function handleViewProject(projectId: string) {
    // 保存当前的筛选状态到 store
    saveFilterState({
      activeFilter,
      selectedDiseaseFilter: null,
      searchName,
      sortBy,
      sortOrder: sortOrder as 'asc' | 'desc',
      currentPage,
      selectedDiseaseId,
      selectedStageId,
      selectedStatusId,
      selectedRecruitmentStatusId,
      activeCardId: null
    });

    goto(`/projects/${projectId}`);
  }

  // 处理编辑项目
  function handleEditProject(projectId: string) {
    console.log('编辑项目，项目ID:', projectId);
    if (!projectId) {
      console.error('无效的项目ID');
      error = '无效的项目ID';
      return;
    }
    goto(`/projects/${projectId}/edit`);
  }

  // 处理删除项目
  function handleDeleteProject(projectId: string) {
    // 查找要删除的项目
    const project = projects.find(p => p.project.project_id === projectId);
    if (!project) {
      error = "找不到要删除的项目";
      return;
    }

    // 设置要删除的项目信息
    projectToDelete = {
      id: projectId,
      name: project.project.project_short_name || project.project.project_name
    };

    // 显示确认对话框
    showDeleteConfirm = true;
  }

  // 确认删除项目
  async function confirmDeleteProject() {
    if (!projectToDelete) return;

    // 验证输入的项目简称是否匹配
    if (confirmProjectName !== projectToDelete.name) {
      deleteConfirmError = "输入的项目简称不匹配，请重新输入";
      return;
    }

    isDeleting = true;
    error = null;
    deleteConfirmError = null;

    try {
      await projectManagementService.deleteProject(projectToDelete.id);
      showDeleteConfirm = false;
      projectToDelete = null;
      confirmProjectName = '';

      // 删除项目后，重新加载所有项目以更新统计数据
      await loadAllProjects();
      // 然后加载当前筛选的项目列表
      loadProjects();
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    } finally {
      isDeleting = false;
    }
  }

  // 取消删除项目
  function cancelDeleteProject() {
    showDeleteConfirm = false;
    projectToDelete = null;
    confirmProjectName = '';
    deleteConfirmError = null;
  }

  // 计算已启动天数
  function calculateDaysSinceStart(startDate: string | undefined): number | null {
    if (!startDate) return null;
    try {
      const start = new Date(startDate);
      const now = new Date();
      // 检查日期是否有效
      if (isNaN(start.getTime())) {
        return null;
      }
      // 计算天数差
      const diffTime = now.getTime() - start.getTime();
      const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
      return diffDays;
    } catch (error) {
      console.error('计算启动天数时出错:', error);
      return null;
    }
  }

  // 处理打开项目文件夹
  async function handleOpenProjectFolder(projectPath: string | undefined) {
    if (!projectPath) {
      error = "项目路径不存在";
      return;
    }

    try {
      const response = await fileSystemService.openFolder(projectPath);
      if (!response.success) {
        error = response.error || "打开项目文件夹失败";
      }
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
    }
  }

  // 处理配置入排标准
  function handleConfigureCriteria(projectId: string) {
    if (!projectId) {
      console.error('无效的项目ID');
      error = '无效的项目ID';
      return;
    }
    goto(`/projects/${projectId}/criteria`);
  }

  // 组件挂载时初始化
  onMount(async () => {
    await initProjectTables();
    await initFilterConfigTables();
    await loadDictionaryItems();

    // 预加载 PI 列表（可按需调整为搜索框）
    try {
      const staff = await staffService.getAllStaff();
      piStaff = staff.map(s => ({ id: s.id as number, name: s.name }));
    } catch (e) {
      console.warn('加载人员列表失败（用于PI选项）:', e);
    }

    await loadFilterConfigs();

    // 首先加载所有项目以获取统计数据
    await loadAllProjects();

    // 从 store 中获取保存的筛选状态
    let filterState: any;
    const unsubscribe = projectFilterStore.subscribe(state => {
      filterState = state;
    });
    unsubscribe();

    if (filterState) {
      // 恢复筛选状态
      activeFilter = filterState.activeFilter;
      searchName = filterState.searchName;
      sortBy = filterState.sortBy;
      sortOrder = filterState.sortOrder as 'asc' | 'desc';
      currentPage = filterState.currentPage;
      selectedDiseaseId = filterState.selectedDiseaseId;
      selectedStageId = filterState.selectedStageId;
      selectedStatusId = filterState.selectedStatusId;
      selectedRecruitmentStatusId = filterState.selectedRecruitmentStatusId;

      // 加载筛选后的项目
      loadProjects();
    } else {
      // 默认应用"在研项目"过滤器
      handleStatusCardClick('ongoing');
    }
  });
</script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6 bg-white p-4 rounded-lg shadow">
    <div class="flex items-center">
      <div class="bg-blue-600 p-2 rounded-lg mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <div>
        <h1 class="text-2xl font-bold text-gray-800">项目管理</h1>
        <p class="text-sm text-gray-500">管理研究项目、人员和申办方</p>
      </div>
      <!-- 顶部紧凑统计标签（无交互，仅展示） -->
      <div class="ml-4 flex flex-wrap items-center gap-2">
        <StatsBadge title="总项目数" count={projectStats.totalProjects} color="blue" />
        <StatsBadge title="在研项目" count={projectStats.ongoingProjects} color="green" />
        <StatsBadge title="已结束项目" count={projectStats.finishedProjects} color="purple" />
        <StatsBadge title="招募中项目" count={projectStats.recruitingProjects} color="orange" />
      </div>
    </div>
    <div class="flex items-center gap-3">
      <!-- 搜索项目名称 -->
      <div class="flex items-center gap-2">
        <div class="relative w-64">
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Search class="h-4 w-4 text-gray-400" />
          </div>
          <Input
            type="text"
            placeholder="搜索项目名称..."
            bind:value={searchName}
            class="pl-9"
            onkeydown={(e: KeyboardEvent) => e.key === 'Enter' && handleSearch()}
          />
          {#if searchName}
            <button
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
              onclick={() => { searchName = ''; handleSearch(); }}
              aria-label="清除搜索"
              title="清除搜索"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          {/if}
        </div>
        <Button
          variant="outline"
          size="sm"
          onclick={handleSearch}
          class="h-10 px-3"
        >
          搜索
        </Button>
      </div>
      <div class="flex gap-2">
        <Button variant="outline" onclick={async () => {
          await loadAllProjects(); // 先更新统计数据
          loadProjects(); // 然后加载当前筛选的项目
        }} class="border-gray-300 hover:bg-gray-50">
          <RefreshCw class="mr-2 h-4 w-4" />
          刷新
        </Button>
        <Button variant="outline" onclick={() => showExportDialog = true} class="border-gray-300 hover:bg-gray-50">
          <FileDown class="mr-2 h-4 w-4" />
          导出
        </Button>
        <Button variant="outline" onclick={() => showCsvImportDialog = true} class="border-gray-300 hover:bg-gray-50">
          <Upload class="mr-2 h-4 w-4" />
          批量导入授权人员
        </Button>
        <Button onclick={() => goto('/projects/new')} class="bg-blue-600 hover:bg-blue-700">
          <PlusCircle class="mr-2 h-4 w-4" />
          新建项目
        </Button>
      </div>
    </div>
  </div>

  <!-- 快捷筛选区域 -->
  <div class="bg-white rounded-lg border border-gray-200 p-4 mb-6">
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-sm font-medium text-gray-700">快捷筛选</h3>
      <div class="flex items-center gap-3">
        <button
          onclick={() => showFilterConfigDialog = true}
          class="text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors"
        >
          + 自定义筛选
        </button>
        <button
          onclick={() => editMode = !editMode}
          class="text-xs text-gray-600 hover:text-gray-800 font-medium transition-colors"
          aria-pressed={editMode}
        >
          {editMode ? '完成' : '编辑'}
        </button>
      </div>
    </div>

    <!-- 统计标签 -->
    <div
      class="flex flex-wrap gap-2"
      role="list"
      ondragover={(e) => {
        if (!draggingId) return;
        e.preventDefault();
      }}
      ondrop={(e) => {
        if (!draggingId) return;
        e.preventDefault();
        // 如果没有命中特定标签，则将拖拽项移动到末尾
        const fromIndex = customFilterTags.findIndex(t => t.id === draggingId);
        if (fromIndex > -1) {
          const moved = customFilterTags[fromIndex];
          const newOrder = [...customFilterTags];
          newOrder.splice(fromIndex, 1);
          newOrder.push(moved);
          customFilterTags = newOrder.slice();
          try { localStorage.setItem('customFilterTagsOrder', JSON.stringify(customFilterTags.map(t => t.id))); } catch (e) { console.warn('[拖拽] 保存本地排序失败', e); }
        }
        // 重置拖拽状态
        draggingId = null;
        dropTargetId = null;
        dropPositionForTarget = null;
      }}
    >
      <!-- 状态统计卡片已移至标题旁显示（此处不再显示四个状态卡片） -->

      <!-- 自定义筛选标签 -->
      {#each customFilterTags as tag (tag.id)}
        <QuickFilterTag
          {tag}
          showActions={editMode}
          draggableEnabled={true}
          isDropTarget={dropTargetId === tag.id}
          dropPosition={dropPositionForTarget || 'before'}
          on:tagdragstart={(e) => {
            draggingId = e.detail.id;
            console.log('[拖拽] start -> draggingId:', draggingId);
          }}
          on:tagdragover={(e) => {
            const { id, clientX, bounding } = e.detail;
            if (!draggingId || draggingId === id) return;
            const midpoint = bounding.left + bounding.width / 2;
            dropTargetId = id;
            dropPositionForTarget = clientX < midpoint ? 'before' : 'after';
            // 可视提示
            // console.log('[拖拽] over -> id:', id, 'position:', dropPositionForTarget);
          }}
          on:tagdrop={(e) => {
            const { id, clientX, bounding } = e.detail;
            if (!draggingId || draggingId === id) return;
            const midpoint = bounding.left + bounding.width / 2;
            const position = clientX < midpoint ? 'before' : 'after';

            // 更新 customFilterTags 顺序
            const fromIndex = customFilterTags.findIndex(t => t.id === draggingId);
            const toIndex = customFilterTags.findIndex(t => t.id === id);
            console.log('[拖拽] drop -> from', draggingId, 'to', id, 'position', position, 'fromIndex', fromIndex, 'toIndex', toIndex);
            console.log('[拖拽] 原顺序:', customFilterTags.map(t => t.id));

            if (fromIndex > -1 && toIndex > -1) {
              const moved = customFilterTags[fromIndex];
              let newOrder = [...customFilterTags];
              // 先删除原位置
              newOrder.splice(fromIndex, 1);
              // 计算插入位置（删除后索引变化）
              let insertIndex = position === 'before' ? toIndex : toIndex + 1;
              if (fromIndex < toIndex) insertIndex -= 1; // 向后拖拽时，目标索引左移一位
              if (insertIndex < 0) insertIndex = 0;
              if (insertIndex > newOrder.length) insertIndex = newOrder.length;

              newOrder.splice(insertIndex, 0, moved);
              console.log('[拖拽] 新顺序:', newOrder.map(t => t.id));
              customFilterTags = newOrder.slice(); // 确保触发响应式
              try { localStorage.setItem('customFilterTagsOrder', JSON.stringify(customFilterTags.map(t => t.id))); } catch (e) { console.warn('[拖拽] 保存本地排序失败', e); }
            }

            // 重置拖拽状态
            draggingId = null;
            dropTargetId = null;
            dropPositionForTarget = null;
          }}
          on:tagdragend={() => {
            // 确保 drop 先触发；这里只做兜底清理
            setTimeout(() => {
              draggingId = null;
              dropTargetId = null;
              dropPositionForTarget = null;
            }, 0);
          }}
          onEdit={(tag) => {
            currentFilterConfig = tag.filterConfig || null;
            showFilterConfigDialog = true;
          }}
          onDelete={(tag) => {
            if (tag.filterConfig?.id && confirm(`确定要删除筛选配置"${tag.label}"吗？`)) {
              deleteFilterConfig(tag.filterConfig.id);
            }
          }}
        />
      {/each}
    </div>
  </div>

  <!-- 错误提示 -->
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <p>{error}</p>
    </div>
  {/if}

  <!-- 项目列表 -->
  <div class="bg-white rounded-lg shadow overflow-hidden overflow-x-auto">
    <div class="min-w-[1000px] w-full">
      <Table>
      <thead>
        <tr class="bg-gray-50 border-b border-gray-200">
          <th class="cursor-pointer w-[5%] text-center" title="项目状态">
            状态
          </th>
          <th class="cursor-pointer w-[12%]" onclick={() => handleSort('project_short_name')}>
            <div class="flex items-center gap-1">
              <span>项目简称</span>
              <span class="text-gray-500">{getSortIcon('project_short_name')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[10%]" onclick={() => handleSort('disease_item_id')}>
            <div class="flex items-center gap-1">
              <span>疾病</span>
              <span class="text-gray-500">{getSortIcon('disease_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[15%]" onclick={() => handleSort('drug_mechanism')}>
            <div class="flex items-center gap-1">
              <span>药物作用机制</span>
              <span class="text-gray-500">{getSortIcon('drug_mechanism')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[10%]" onclick={() => handleSort('sponsor_item_id')}>
            <div class="flex items-center gap-1">
              <span>申办方</span>
              <span class="text-gray-500">{getSortIcon('sponsor_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[7%]" onclick={() => handleSort('project_stage_item_id')}>
            <div class="flex items-center gap-1">
              <span>研究分期</span>
              <span class="text-gray-500">{getSortIcon('project_stage_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[7%]" onclick={() => handleSort('project_status_item_id')}>
            <div class="flex items-center gap-1">
              <span>研究阶段</span>
              <span class="text-gray-500">{getSortIcon('project_status_item_id')}</span>
            </div>
          </th>
          <th class="cursor-pointer w-[7%]" onclick={() => handleSort('recruitment_status_item_id')}>
            <div class="flex items-center gap-1">
              <span>招募状态</span>
              <span class="text-gray-500">{getSortIcon('recruitment_status_item_id')}</span>
            </div>
          </th>
          <th class="w-[15%]">研究人员</th>
          <th class="w-[8%] text-center">入排标准</th>
          <th class="cursor-pointer w-[8%]" onclick={() => handleSort('project_start_date')}>
            <div class="flex items-center gap-1">
              <span>已启动</span>
              <span class="text-gray-500">{getSortIcon('project_start_date')}</span>
            </div>
          </th>
          <th class="text-center w-[10%]">操作</th>
        </tr>
      </thead>
      <tbody>
        {#if isLoading}
          <tr>
            <td colspan="12" class="text-center py-8">
              <div class="flex justify-center items-center">
                <div class="animate-spin h-5 w-5 mr-3 border-2 border-blue-500 border-t-transparent rounded-full"></div>
                加载中...
              </div>
            </td>
          </tr>
        {:else if projects.length === 0}
          <tr>
            <td colspan="12" class="text-center py-8">
              <div class="flex flex-col items-center justify-center text-gray-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p>暂无项目数据</p>
              </div>
            </td>
          </tr>
        {:else}
          {#each projects as project, index}
            <tr
              class="cursor-pointer hover:bg-blue-50 transition-colors duration-150 ease-in-out {index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}"
              onclick={() => handleViewProject(project.project.project_id || '')}
            >
              <!-- 状态指示器 -->
              <td class="text-center py-3">
                <div class="flex flex-col items-center justify-center gap-1">
                  <!-- 研究状态指示器 -->
                  <div
                    class="w-3 h-3 rounded-full border border-gray-200"
                    class:bg-green-500={project.project_status?.item_value === '在研'}
                    class:bg-gray-500={project.project_status?.item_value === '已结束'}
                    class:bg-yellow-500={project.project_status?.item_value === '准备中'}
                    class:bg-red-500={project.project_status?.item_value === '暂停'}
                    class:bg-blue-500={!project.project_status?.item_value}
                    title={`研究状态: ${project.project_status?.item_value || '未设置'}`}
                  ></div>
                  <!-- 招募状态指示器 -->
                  <div
                    class="w-3 h-3 transform rotate-45 border border-gray-200"
                    class:bg-orange-500={project.recruitment_status?.item_value === '招募中'}
                    class:bg-green-500={project.recruitment_status?.item_value === '招募完成'}
                    class:bg-gray-500={project.recruitment_status?.item_value === '未招募'}
                    class:bg-blue-500={!project.recruitment_status?.item_value}
                    title={`招募状态: ${project.recruitment_status?.item_value || '未设置'}`}
                  ></div>
                </div>
              </td>

              <!-- 项目简称 -->
              <td class="py-3">
                <div class="flex items-center gap-2">
                  <div class="flex-shrink-0 text-blue-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <div class="truncate font-medium" title={project.project.project_short_name}>
                    {project.project.project_short_name}
                  </div>
                </div>
              </td>

              <!-- 疾病 -->
              <td class="py-3">
                <div class="px-2 py-1 bg-blue-50 text-blue-700 rounded-md text-xs inline-block max-w-full truncate" title={project.disease?.item_value || '-'}>
                  {project.disease?.item_value || '-'}
                </div>
              </td>

              <!-- 药物作用机制 -->
              <td class="py-3">
                {#if project.project.drug_mechanism}
                  <div class="text-sm text-gray-700 max-w-full" title={project.project.drug_mechanism}>
                    <span class="line-clamp-2">
                      {project.project.drug_mechanism.length > 50
                        ? project.project.drug_mechanism.substring(0, 50) + '...'
                        : project.project.drug_mechanism}
                    </span>
                  </div>
                {:else}
                  <span class="text-gray-400 text-sm">未填写</span>
                {/if}
              </td>

              <!-- 申办方 -->
              <td class="py-3">
                {#if project.sponsors && project.sponsors.length > 0}
                  <div class="flex flex-wrap gap-1">
                    {#each project.sponsors as sponsor, i}
                      {#if i < 2}
                        <div class="px-2 py-1 bg-purple-50 text-purple-700 rounded-md text-xs inline-block truncate max-w-[120px]" title={sponsor.sponsor?.item_value || '-'}>
                          {sponsor.sponsor?.item_value || '-'}
                        </div>
                        {#if i < project.sponsors.length - 1 && i < 1}
                          <span class="text-gray-400 mx-0.5">+</span>
                        {/if}
                      {:else if i === 2}
                        <div class="px-2 py-1 bg-gray-100 text-gray-600 rounded-md text-xs inline-block">
                          +{project.sponsors.length - 2}
                        </div>
                      {/if}
                    {/each}
                  </div>
                {:else}
                  <span class="text-gray-400 text-sm">-</span>
                {/if}
              </td>

              <!-- 研究分期 -->
              <td class="py-3">
                <div class="px-2 py-1 bg-green-50 text-green-700 rounded-md text-xs inline-block max-w-full truncate" title={project.project_stage?.item_value || '-'}>
                  {project.project_stage?.item_value || '-'}
                </div>
              </td>

              <!-- 研究阶段 -->
              <td class="py-3">
                <div
                  class="px-2 py-1 rounded-md text-xs inline-block max-w-full truncate"
                  class:bg-green-50={project.project_status?.item_value === '在研'}
                  class:text-green-700={project.project_status?.item_value === '在研'}
                  class:bg-gray-50={project.project_status?.item_value === '已结束'}
                  class:text-gray-700={project.project_status?.item_value === '已结束'}
                  class:bg-yellow-50={project.project_status?.item_value === '准备中'}
                  class:text-yellow-700={project.project_status?.item_value === '准备中'}
                  class:bg-red-50={project.project_status?.item_value === '暂停'}
                  class:text-red-700={project.project_status?.item_value === '暂停'}
                  class:bg-blue-50={!project.project_status?.item_value}
                  class:text-blue-700={!project.project_status?.item_value}
                  title={project.project_status?.item_value || '-'}
                >
                  {project.project_status?.item_value || '-'}
                </div>
              </td>

              <!-- 招募状态 -->
              <td class="py-3">
                <div
                  class="px-2 py-1 rounded-md text-xs inline-block max-w-full truncate"
                  class:bg-orange-50={project.recruitment_status?.item_value === '招募中'}
                  class:text-orange-700={project.recruitment_status?.item_value === '招募中'}
                  class:bg-green-50={project.recruitment_status?.item_value === '招募完成'}
                  class:text-green-700={project.recruitment_status?.item_value === '招募完成'}
                  class:bg-gray-50={project.recruitment_status?.item_value === '未招募'}
                  class:text-gray-700={project.recruitment_status?.item_value === '未招募'}
                  class:bg-blue-50={!project.recruitment_status?.item_value}
                  class:text-blue-700={!project.recruitment_status?.item_value}
                  title={project.recruitment_status?.item_value || '-'}
                >
                  {project.recruitment_status?.item_value || '-'}
                </div>
              </td>

              <!-- 研究人员 -->
              <td class="py-3">
                {#if project.personnel && project.personnel.length > 0}
                  {@const criticalRoles = ['主要研究者', '临床协调员(CRC)']}
                  {@const allGroups = groupPersonnelByRole(project.personnel)}
                  {@const criticalGroups = allGroups.filter(g => criticalRoles.includes(g.roleName))}
                  {@const otherGroups = allGroups.filter(g => !criticalRoles.includes(g.roleName))}
                  {@const missingRoles = criticalRoles.filter(role => !allGroups.map(g => g.roleName).includes(role))}

                  <div class="flex items-center gap-2">
                    <div class="flex flex-wrap gap-1.5 flex-1">
                      <!-- 关键角色简洁显示 -->
                      {#each criticalRoles as roleName}
                        {@const roleGroup = criticalGroups.find(g => g.roleName === roleName)}
                        <div class="group relative">
                          <div class="bg-blue-100 px-2 py-1 rounded-md border border-blue-200">
                            <span class="text-xs font-bold text-blue-800">
                              {roleName.replace('临床协调员(CRC)', 'CRC').replace('主要研究者', 'PI')}：
                            </span>
                            {#if roleGroup && roleGroup.personnel.length > 0}
                              <span class="text-xs font-medium text-blue-700">
                                {roleGroup.personnel.map(p => p.person.personnel?.name || '未知').join('、')}
                              </span>
                            {:else}
                              <span class="text-xs text-gray-500 italic">未分配</span>
                            {/if}
                          </div>
                          <!-- 悬浮显示详细信息 -->
                          {#if roleGroup && roleGroup.personnel.length > 1}
                            <div class="absolute left-0 top-full mt-1 z-10 bg-white shadow-lg rounded-md p-2 w-48 hidden group-hover:block border border-blue-200">
                              <div class="text-xs font-bold text-blue-800 mb-1 pb-1 border-b border-blue-200">{roleName}</div>
                              <div class="flex flex-col gap-1">
                                {#each roleGroup.personnel as p}
                                  <div class="text-xs text-blue-700 font-medium">{p.person.personnel?.name || '未知'}</div>
                                {/each}
                              </div>
                            </div>
                          {/if}
                        </div>
                      {/each}

                      <!-- 其他角色简化显示 -->
                      {#if otherGroups.length > 0}
                        <div class="group relative">
                          <div class="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-md hover:bg-gray-200 transition-colors">
                            <span class="text-xs font-medium text-gray-600">其他角色</span>
                            <span class="text-xs text-gray-500">{otherGroups.reduce((total, g) => total + g.personnel.length, 0)}</span>
                          </div>
                          <!-- 悬浮显示其他角色详情 -->
                          <div class="absolute left-0 top-full mt-1 z-10 bg-white shadow-lg rounded-md p-2 w-56 hidden group-hover:block">
                            <div class="text-xs font-medium text-gray-700 mb-2">其他研究人员</div>
                            <div class="space-y-2 max-h-32 overflow-y-auto">
                              {#each otherGroups as roleGroup}
                                <div>
                                  <div class="text-xs font-medium text-gray-600 mb-1">{roleGroup.roleName} ({roleGroup.personnel.length}人)</div>
                                  <div class="flex flex-wrap gap-1">
                                    {#each roleGroup.personnel as p}
                                      <span class="text-xs text-gray-500 bg-gray-50 px-1 py-0.5 rounded">
                                        {p.person.personnel?.name || '未知'}
                                      </span>
                                    {/each}
                                  </div>
                                </div>
                              {/each}
                            </div>
                          </div>
                        </div>
                      {/if}
                    </div>
                    <!-- 质量控制指示器 -->
                    {#if missingRoles.length > 0}
                      <div class="group relative">
                        <div class="h-4 w-4 text-orange-500" title="缺失关键角色">
                          <AlertTriangle class="h-4 w-4" />
                        </div>
                        <!-- 悬浮显示缺失角色 -->
                        <div class="absolute right-0 top-full mt-1 z-10 bg-white shadow-lg rounded-md p-2 w-32 hidden group-hover:block">
                          <div class="text-xs font-medium text-orange-700 mb-1">缺失关键角色:</div>
                          <div class="text-xs text-orange-600">
                            {missingRoles.join(', ')}
                          </div>
                        </div>
                      </div>
                    {/if}
                  </div>
                {:else}
                  <div class="flex items-center gap-2">
                    <span class="text-gray-400 text-xs">暂无人员</span>
                    <div class="h-4 w-4 text-red-500" title="缺失所有关键角色">
                      <AlertTriangle class="h-4 w-4" />
                    </div>
                  </div>
                {/if}
              </td>

              <!-- 入排标准状态 -->
              <td class="py-3 text-center">
                {#if project.criteria_stats?.has_criteria}
                  <div class="group relative inline-block">
                    <!-- 已配置状态 -->
                    <div class="flex items-center justify-center gap-1 px-2 py-1 bg-green-50 text-green-700 rounded-md text-xs cursor-pointer hover:bg-green-100 transition-colors">
                      <CheckCircle class="h-3 w-3" />
                      <span>已配置</span>
                    </div>
                    <!-- 悬浮提示框 -->
                    <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-20 bg-gray-900 text-white text-xs rounded-md p-2 w-48 hidden group-hover:block">
                      <div class="text-center mb-1 font-medium">入排标准配置</div>
                      <div class="flex justify-between items-center mb-1">
                        <span>入组标准:</span>
                        <span class="font-medium">{project.criteria_stats.inclusion_count}条</span>
                      </div>
                      <div class="flex justify-between items-center">
                        <span>排除标准:</span>
                        <span class="font-medium">{project.criteria_stats.exclusion_count}条</span>
                      </div>
                      <!-- 小箭头 -->
                      <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
                    </div>
                  </div>
                {:else}
                  <!-- 未配置状态 -->
                  <div class="flex items-center justify-center gap-1 px-2 py-1 bg-red-50 text-red-700 rounded-md text-xs">
                    <AlertTriangle class="h-3 w-3" />
                    <span>未配置</span>
                  </div>
                {/if}
              </td>

              <!-- 已启动 -->
              <td class="py-3">
                {#if project.project.project_start_date}
                  {@const days = calculateDaysSinceStart(project.project.project_start_date)}
                  <div class="flex items-center gap-2">
                    <div
                      class="text-base font-medium tabular-nums"
                      class:text-green-600={days !== null && days > 180}
                      class:text-blue-600={days !== null && days > 90 && days <= 180}
                      class:text-orange-500={days !== null && days <= 90}
                    >
                      {days !== null ? days : '-'}
                    </div>
                    <div class="flex flex-col">
                      <span class="text-xs text-gray-500">天</span>
                      <span class="text-xs text-gray-400" title={project.project.project_start_date}>
                        {project.project.project_start_date.split('T')[0].replace(/-/g, '/')}
                      </span>
                    </div>
                  </div>
                {:else}
                  <span class="text-gray-400">-</span>
                {/if}
              </td>

              <!-- 操作 -->
              <td class="text-center py-3" onclick={(e) => e.stopPropagation()}>
                <div class="flex justify-center items-center gap-1">
                  <!-- 编辑按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-blue-600 hover:bg-blue-50"
                    title="编辑项目"
                    onclick={() => handleEditProject(project.project.project_id || '')}
                  >
                    <Pencil class="h-4 w-4" />
                  </Button>

                  <!-- 配置入排标准按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-purple-600 hover:bg-purple-50"
                    title="配置入排标准"
                    onclick={() => handleConfigureCriteria(project.project.project_id || '')}
                  >
                    <ClipboardList class="h-4 w-4" />
                  </Button>

                  <!-- 文件夹按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-green-600 hover:bg-green-50"
                    title="打开项目文件夹"
                    onclick={() => handleOpenProjectFolder(project.project.project_path)}
                  >
                    <Folder class="h-4 w-4" />
                  </Button>

                  <!-- 删除按钮 -->
                  <Button
                    variant="ghost"
                    size="icon"
                    class="h-8 w-8 p-0 text-gray-600 hover:text-red-600 hover:bg-red-50"
                    title="删除项目"
                    onclick={() => handleDeleteProject(project.project.project_id || '')}
                  >
                    <Trash class="h-4 w-4" />
                  </Button>
                </div>
              </td>
            </tr>
          {/each}
        {/if}
      </tbody>
    </Table>
    </div>
  </div>

  <!-- 分页 -->
  {#if total > 0}
    <div class="flex justify-between items-center mt-4 bg-white p-3 rounded-lg shadow">
      <div class="flex items-center gap-2">
        <span class="text-sm text-gray-600">每页显示:</span>
        <select
          bind:value={pageSize}
          onchange={() => { currentPage = 1; loadProjects(); }}
          class="h-8 w-20 rounded-md border border-input bg-background px-3 py-1 text-sm"
        >
          {#each [5, 10, 20, 50, 100] as size}
            <option value={size}>{size}</option>
          {/each}
        </select>
        <span class="text-sm text-gray-600">共 <span class="font-medium text-gray-800">{total}</span> 条记录，当前第 <span class="font-medium text-gray-800">{currentPage}</span> 页</span>
      </div>
      <div class="flex gap-1">
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          on:click={() => handlePageChange(1)}
          class="h-8 px-2 min-w-[40px]"
        >
          首页
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage === 1}
          on:click={() => handlePageChange(currentPage - 1)}
          class="h-8 px-2 min-w-[40px]"
        >
          上一页
        </Button>

        <!-- 页码按钮 -->
        {#if total > 0}
          {#each Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => {
            // 计算显示哪些页码
            let pageNumbers = [];
            const totalPages = Math.ceil(total / pageSize);

            if (totalPages <= 5) {
              // 如果总页数小于等于5，显示所有页码
              pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);
            } else {
              // 否则显示当前页附近的页码
              if (currentPage <= 3) {
                pageNumbers = [1, 2, 3, 4, 5];
              } else if (currentPage >= totalPages - 2) {
                pageNumbers = [totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
              } else {
                pageNumbers = [currentPage - 2, currentPage - 1, currentPage, currentPage + 1, currentPage + 2];
              }
            }

            return pageNumbers[i];
          }) as page}
            <Button
              variant={page === currentPage ? 'default' : 'outline'}
              size="sm"
              on:click={() => handlePageChange(page)}
              class="h-8 w-8 p-0 font-medium"
            >
              {page}
            </Button>
          {/each}
        {/if}

        <Button
          variant="outline"
          size="sm"
          disabled={currentPage * pageSize >= total}
          on:click={() => handlePageChange(currentPage + 1)}
          class="h-8 px-2 min-w-[40px]"
        >
          下一页
        </Button>
        <Button
          variant="outline"
          size="sm"
          disabled={currentPage * pageSize >= total}
          on:click={() => handlePageChange(Math.ceil(total / pageSize))}
          class="h-8 px-2 min-w-[40px]"
        >
          末页
        </Button>
      </div>
    </div>
  {/if}

  <!-- 删除确认对话框 -->
  {#if showDeleteConfirm && projectToDelete}
    <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 max-w-md w-full">
        <h3 class="text-xl font-bold mb-4">确认删除项目</h3>
        <p class="mb-4">您确定要删除项目 <span class="font-semibold">{projectToDelete.name}</span> 吗？此操作将删除所有相关数据，且不可恢复。</p>

        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm text-yellow-700">
                为确认删除，请输入项目简称：<strong>{projectToDelete.name}</strong>
              </p>
            </div>
          </div>
        </div>

        <div class="mb-4">
          <label for="confirmProjectName" class="block text-sm font-medium text-gray-700 mb-1">
            项目简称
          </label>
          <div class="flex gap-2">
            <div class="flex-grow">
              <Input
                id="confirmProjectName"
                type="text"
                placeholder="请输入项目简称以确认删除"
                bind:value={confirmProjectName}
                class={deleteConfirmError ? "border-red-500" : ""}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              class="whitespace-nowrap"
              onclick={() => confirmProjectName = projectToDelete?.name || ''}
            >
              自动填入
            </Button>
          </div>
          {#if deleteConfirmError}
            <p class="mt-1 text-sm text-red-600">{deleteConfirmError}</p>
            <p class="text-xs text-gray-500">正确的项目简称是: <span class="font-mono bg-gray-100 px-1 rounded">{projectToDelete?.name}</span></p>
          {/if}
        </div>

        <div class="flex justify-end gap-2">
          <Button variant="outline" onclick={cancelDeleteProject} disabled={isDeleting}>
            取消
          </Button>
          <Button variant="destructive" onclick={confirmDeleteProject} disabled={isDeleting || !confirmProjectName}>
            {#if isDeleting}
              <div class="animate-spin h-4 w-4 mr-2 border-2 border-white rounded-full border-t-transparent"></div>
              删除中...
            {:else}
              确认删除
            {/if}
          </Button>
        </div>
      </div>
    </div>
  {/if}

  <!-- 导出项目对话框 -->
  <ExportProjectsDialog bind:open={showExportDialog} projects={projects} />

  <!-- CSV导入对话框 -->
  <CsvImportDialog
    open={showCsvImportDialog}
    onClose={() => {
      showCsvImportDialog = false;
      // 导入完成后刷新项目列表
      loadAllProjects();
      loadProjects();
    }}
  />

  <!-- 筛选配置对话框 -->
  <FilterConfigDialog
    isOpen={showFilterConfigDialog}
    config={currentFilterConfig}
    availableOptions={{
      project_status_item_id: statuses.map(s => ({ value: s.item_id, label: s.item_value })),
      recruitment_status_item_id: recruitmentStatuses.map(s => ({ value: s.item_id, label: s.item_value })),
      disease_item_id: diseases.map(d => ({ value: d.item_id, label: d.item_value })),
      project_stage_item_id: stages.map(s => ({ value: s.item_id, label: s.item_value })),
      sponsor_item_ids: sponsors.map(s => ({ value: s.item_id, label: s.item_value })),
      pi_personnel_ids: piStaff.map(p => ({ value: p.id, label: p.name }))
    }}
    on:save={(event) => saveFilterConfig(event.detail)}
    on:cancel={() => {
      showFilterConfigDialog = false;
      currentFilterConfig = null;
    }}
  />
</div>
